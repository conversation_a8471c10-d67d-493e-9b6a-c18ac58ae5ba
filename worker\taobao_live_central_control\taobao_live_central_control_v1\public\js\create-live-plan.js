/**
 * 创建直播计划功能模块
 */

// 全局变量
let selectedAnchors = [];
let selectedTime = 'today';

/**
 * 显示创建直播计划弹窗
 */
function showCreateLivePlanModal() {
    // 加载主播列表
    loadAnchorsForCreatePlan();
    // 更新时间显示
    updateTimeDisplay();
    // 显示弹窗
    $('#createLivePlanModal').removeClass('hidden');
}

/**
 * 隐藏创建直播计划弹窗
 */
function hideCreateLivePlanModal() {
    $('#createLivePlanModal').addClass('hidden');
    // 重置选择状态
    selectedAnchors = [];
    selectedTime = 'today';
    $('.anchor-btn').removeClass('anchor-btn-active');
    $('.time-btn').removeClass('time-btn-active');
    $('.time-btn[data-time="today"]').addClass('time-btn-active');
}

/**
 * 加载主播列表用于创建计划
 */
async function loadAnchorsForCreatePlan() {
    try {
        const response = await fetch('/api/anchors?mode=full', {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取主播列表失败');
        }

        if (data.anchors && data.anchors.length > 0) {
            // 按sort字段正序排列主播
            const sortedAnchors = [...data.anchors].sort((a, b) => {
                const sortA = parseInt(a.sort) || 0;
                const sortB = parseInt(b.sort) || 0;
                return sortA - sortB;
            });

            // 生成主播按钮
            const anchorButtonsContainer = $('#createPlanAnchorButtons');
            anchorButtonsContainer.empty();

            // 添加主播按钮
            sortedAnchors.forEach(anchor => {
                const button = $(`
                    <button type="button" class="anchor-btn" data-anchor="${anchor.anchor_name}">
                        ${anchor.anchor_name}
                    </button>
                `);
                anchorButtonsContainer.append(button);
            });

            // 绑定按钮点击事件
            bindCreatePlanAnchorButtonEvents();
        }
    } catch (error) {
        console.error('加载主播列表失败:', error);
        layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
    }
}

/**
 * 绑定创建计划页面的主播按钮点击事件
 */
function bindCreatePlanAnchorButtonEvents() {
    $('#createPlanAnchorButtons .anchor-btn').off('click').on('click', function() {
        const anchorName = $(this).data('anchor');
        
        // 切换选中状态
        if ($(this).hasClass('anchor-btn-active')) {
            // 取消选中
            $(this).removeClass('anchor-btn-active');
            selectedAnchors = selectedAnchors.filter(name => name !== anchorName);
        } else {
            // 选中
            $(this).addClass('anchor-btn-active');
            selectedAnchors.push(anchorName);
        }
        
        console.log('已选择主播:', selectedAnchors);
    });

    // 绑定时间按钮点击事件
    $('.time-btn').off('click').on('click', function() {
        const timeType = $(this).data('time');
        
        // 更新按钮状态
        $('.time-btn').removeClass('time-btn-active');
        $(this).addClass('time-btn-active');
        
        // 更新选中时间
        selectedTime = timeType;
        updateTimeDisplay();
        
        console.log('已选择时间:', selectedTime);
    });

    // 绑定确认创建按钮
    $('#confirmCreateLivePlan').off('click').on('click', function() {
        createLivePlans();
    });
}

/**
 * 更新时间显示
 */
function updateTimeDisplay() {
    const now = new Date();
    let targetDate;
    
    if (selectedTime === 'today') {
        targetDate = new Date(now);
    } else if (selectedTime === 'tomorrow') {
        targetDate = new Date(now);
        targetDate.setDate(targetDate.getDate() + 1);
    }
    
    // 设置为23:55
    targetDate.setHours(23, 55, 0, 0);
    
    const dateStr = targetDate.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric'
    });
    
    $('#selectedTimeDisplay').text(`${selectedTime === 'today' ? '今日' : '明日'} 23:55 (UTC+8) - ${dateStr}`);
}

/**
 * 创建直播计划
 */
async function createLivePlans() {
    if (selectedAnchors.length === 0) {
        layer.msg('请至少选择一个主播', { icon: 2 });
        return;
    }

    // 计算目标时间戳
    const now = new Date();
    let targetDate;

    if (selectedTime === 'today') {
        targetDate = new Date(now);
    } else if (selectedTime === 'tomorrow') {
        targetDate = new Date(now);
        targetDate.setDate(targetDate.getDate() + 1);
    }

    // 设置为23:55
    targetDate.setHours(23, 55, 0, 0);
    const appointmentTime = targetDate.getTime();

    // 设置结束时间为开始时间后4小时
    const liveEndTime = appointmentTime + (4 * 60 * 60 * 1000);

    // 显示加载状态
    const loadingIndex = layer.msg('正在创建直播计划...', {
        icon: 16,
        shade: 0.3,
        time: 0
    });

    try {
        // 为每个选中的主播创建直播计划
        const results = [];
        const successfulAnchors = []; // 记录创建成功的主播

        for (const anchorName of selectedAnchors) {
            try {
                const result = await createSingleLivePlan(anchorName, appointmentTime, liveEndTime);
                results.push({
                    anchorName,
                    success: true,
                    liveId: result.liveId
                });
                successfulAnchors.push(anchorName);
            } catch (error) {
                console.error(`为主播 ${anchorName} 创建直播计划失败:`, error);
                results.push({
                    anchorName,
                    success: false,
                    error: error.message
                });
            }
        }

        // 关闭创建阶段的加载提示
        layer.close(loadingIndex);

        // 如果有成功创建的主播，开始同步直播计划
        let syncResults = [];
        if (successfulAnchors.length > 0) {
            const syncLoadingIndex = layer.msg('正在同步直播计划...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            try {
                // 为每个成功创建的主播同步直播计划
                for (const anchorName of successfulAnchors) {
                    try {
                        const syncResult = await syncAnchorLivePlan(anchorName);
                        syncResults.push({
                            anchorName,
                            syncSuccess: true,
                            syncData: syncResult
                        });
                    } catch (error) {
                        console.error(`同步主播 ${anchorName} 直播计划失败:`, error);
                        syncResults.push({
                            anchorName,
                            syncSuccess: false,
                            syncError: error.message
                        });
                    }
                }
                layer.close(syncLoadingIndex);
            } catch (error) {
                layer.close(syncLoadingIndex);
                console.error('同步直播计划过程中发生错误:', error);
                // 如果同步过程中发生错误，仍然显示已有的同步结果
            }
        }

        // 统一处理结果显示
        showCreateAndSyncResults(results, syncResults);

    } catch (error) {
        // 确保关闭所有可能的加载提示
        try {
            layer.close(loadingIndex);
        } catch (e) {
            // 忽略关闭失败的错误
        }
        console.error('创建直播计划失败:', error);
        layer.msg('创建直播计划失败: ' + error.message, { icon: 2 });
    }
}

/**
 * 为单个主播创建直播计划
 */
async function createSingleLivePlan(anchorName, appointmentTime, liveEndTime) {
    try {
        const response = await fetch('/api/live-plans/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...addApiKeyHeader()
            },
            body: JSON.stringify({
                anchorName: anchorName,
                appointmentTime: appointmentTime,
                liveEndTime: liveEndTime
            })
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                throw new Error('认证失败');
            }
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || result.error || '创建失败');
        }

        return result;
    } catch (error) {
        console.error(`为主播 ${anchorName} 创建直播计划失败:`, error);
        throw error;
    }
}

/**
 * 同步单个主播的直播计划
 */
async function syncAnchorLivePlan(anchorName) {
    try {
        const response = await fetch('/api/live-plans/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...addApiKeyHeader()
            },
            body: JSON.stringify({
                anchorName: anchorName
            })
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                throw new Error('认证失败');
            }
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || result.error || '同步失败');
        }

        console.log(`主播 ${anchorName} 直播计划同步成功:`, result.data);
        return result;
    } catch (error) {
        console.error(`同步主播 ${anchorName} 直播计划失败:`, error);
        throw error;
    }
}

/**
 * 统一显示创建和同步结果
 */
function showCreateAndSyncResults(createResults, syncResults) {
    // 计算统计数据
    const createSuccessCount = createResults.filter(r => r.success).length;
    const createFailCount = createResults.filter(r => !r.success).length;
    const syncSuccessCount = syncResults.filter(r => r.syncSuccess).length;
    const syncFailCount = syncResults.filter(r => !r.syncSuccess).length;

    // 构建消息
    let message = `创建完成！成功: ${createSuccessCount}个，失败: ${createFailCount}个`;

    // 如果有同步操作，添加同步结果
    if (syncResults.length > 0) {
        message += `\n同步完成！成功: ${syncSuccessCount}个，失败: ${syncFailCount}个`;
    }

    // 添加失败详情
    if (createFailCount > 0) {
        const failedAnchors = createResults.filter(r => !r.success).map(r => r.anchorName).join(', ');
        message += `\n创建失败的主播: ${failedAnchors}`;
    }

    if (syncFailCount > 0) {
        const syncFailedAnchors = syncResults.filter(r => !r.syncSuccess).map(r => r.anchorName).join(', ');
        message += `\n同步失败的主播: ${syncFailedAnchors}`;
    }

    // 显示消息
    const isSuccess = createSuccessCount > 0;
    layer.msg(message, {
        icon: isSuccess ? 1 : 2,
        time: 5000
    });

    // 如果有成功的创建，刷新页面数据并关闭弹窗
    if (isSuccess) {
        hideCreateLivePlanModal();
        if (typeof loadLivePlansAndStats === 'function') {
            loadLivePlansAndStats();
        }
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('创建直播计划模块已加载');
});
