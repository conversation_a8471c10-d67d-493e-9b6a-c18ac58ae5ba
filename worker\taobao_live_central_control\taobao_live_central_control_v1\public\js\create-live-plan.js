/**
 * 创建直播计划功能模块
 */

// 全局变量
let selectedAnchors = [];
let selectedTime = 'today';

/**
 * 显示创建直播计划弹窗
 */
function showCreateLivePlanModal() {
    // 加载主播列表
    loadAnchorsForCreatePlan();
    // 更新时间显示
    updateTimeDisplay();
    // 显示弹窗
    $('#createLivePlanModal').removeClass('hidden');
}

/**
 * 隐藏创建直播计划弹窗
 */
function hideCreateLivePlanModal() {
    $('#createLivePlanModal').addClass('hidden');
    // 重置选择状态
    selectedAnchors = [];
    selectedTime = 'today';
    $('.anchor-btn').removeClass('anchor-btn-active');
    $('.time-btn').removeClass('time-btn-active');
    $('.time-btn[data-time="today"]').addClass('time-btn-active');
}

/**
 * 加载主播列表用于创建计划
 */
async function loadAnchorsForCreatePlan() {
    try {
        const response = await fetch('/api/anchors?mode=full', {
            headers: addApiKeyHeader()
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                return;
            }
        }

        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || '获取主播列表失败');
        }

        if (data.anchors && data.anchors.length > 0) {
            // 按sort字段正序排列主播
            const sortedAnchors = [...data.anchors].sort((a, b) => {
                const sortA = parseInt(a.sort) || 0;
                const sortB = parseInt(b.sort) || 0;
                return sortA - sortB;
            });

            // 生成主播按钮
            const anchorButtonsContainer = $('#createPlanAnchorButtons');
            anchorButtonsContainer.empty();

            // 添加主播按钮
            sortedAnchors.forEach(anchor => {
                const button = $(`
                    <button type="button" class="anchor-btn" data-anchor="${anchor.anchor_name}">
                        ${anchor.anchor_name}
                    </button>
                `);
                anchorButtonsContainer.append(button);
            });

            // 绑定按钮点击事件
            bindCreatePlanAnchorButtonEvents();
        }
    } catch (error) {
        console.error('加载主播列表失败:', error);
        layer.msg('加载主播列表失败: ' + error.message, { icon: 2 });
    }
}

/**
 * 绑定创建计划页面的主播按钮点击事件
 */
function bindCreatePlanAnchorButtonEvents() {
    $('#createPlanAnchorButtons .anchor-btn').off('click').on('click', function() {
        const anchorName = $(this).data('anchor');
        
        // 切换选中状态
        if ($(this).hasClass('anchor-btn-active')) {
            // 取消选中
            $(this).removeClass('anchor-btn-active');
            selectedAnchors = selectedAnchors.filter(name => name !== anchorName);
        } else {
            // 选中
            $(this).addClass('anchor-btn-active');
            selectedAnchors.push(anchorName);
        }
        
        console.log('已选择主播:', selectedAnchors);
    });

    // 绑定时间按钮点击事件
    $('.time-btn').off('click').on('click', function() {
        const timeType = $(this).data('time');
        
        // 更新按钮状态
        $('.time-btn').removeClass('time-btn-active');
        $(this).addClass('time-btn-active');
        
        // 更新选中时间
        selectedTime = timeType;
        updateTimeDisplay();
        
        console.log('已选择时间:', selectedTime);
    });

    // 绑定确认创建按钮
    $('#confirmCreateLivePlan').off('click').on('click', function() {
        createLivePlans();
    });
}

/**
 * 更新时间显示
 */
function updateTimeDisplay() {
    const now = new Date();
    let targetDate;
    
    if (selectedTime === 'today') {
        targetDate = new Date(now);
    } else if (selectedTime === 'tomorrow') {
        targetDate = new Date(now);
        targetDate.setDate(targetDate.getDate() + 1);
    }
    
    // 设置为23:55
    targetDate.setHours(23, 55, 0, 0);
    
    const dateStr = targetDate.toLocaleDateString('zh-CN', {
        month: 'long',
        day: 'numeric'
    });
    
    $('#selectedTimeDisplay').text(`${selectedTime === 'today' ? '今日' : '明日'} 23:55 (UTC+8) - ${dateStr}`);
}

/**
 * 创建直播计划
 */
async function createLivePlans() {
    if (selectedAnchors.length === 0) {
        layer.msg('请至少选择一个主播', { icon: 2 });
        return;
    }

    // 计算目标时间戳
    const now = new Date();
    let targetDate;

    if (selectedTime === 'today') {
        targetDate = new Date(now);
    } else if (selectedTime === 'tomorrow') {
        targetDate = new Date(now);
        targetDate.setDate(targetDate.getDate() + 1);
    }

    // 设置为23:55
    targetDate.setHours(23, 55, 0, 0);
    const appointmentTime = targetDate.getTime();

    // 设置结束时间为开始时间后4小时
    const liveEndTime = appointmentTime + (4 * 60 * 60 * 1000);

    // 显示加载状态
    const loadingIndex = layer.msg('正在创建直播计划...', {
        icon: 16,
        shade: 0.3,
        time: 0
    });

    try {
        // 为每个选中的主播创建直播计划
        const results = [];
        const successfulAnchors = []; // 记录创建成功的主播

        for (const anchorName of selectedAnchors) {
            try {
                const result = await createSingleLivePlan(anchorName, appointmentTime, liveEndTime);
                results.push({
                    anchorName,
                    success: true,
                    liveId: result.liveId
                });
                successfulAnchors.push(anchorName);
            } catch (error) {
                console.error(`为主播 ${anchorName} 创建直播计划失败:`, error);
                results.push({
                    anchorName,
                    success: false,
                    error: error.message
                });
            }
        }

        // 如果有成功创建的主播，开始同步直播计划
        if (successfulAnchors.length > 0) {
            // 更新加载提示
            layer.close(loadingIndex);
            const syncLoadingIndex = layer.msg('正在同步直播计划...', {
                icon: 16,
                shade: 0.3,
                time: 0
            });

            try {
                // 为每个成功创建的主播同步直播计划
                const syncResults = [];
                for (const anchorName of successfulAnchors) {
                    try {
                        const syncResult = await syncAnchorLivePlan(anchorName);
                        syncResults.push({
                            anchorName,
                            syncSuccess: true,
                            syncData: syncResult
                        });
                    } catch (error) {
                        console.error(`同步主播 ${anchorName} 直播计划失败:`, error);
                        syncResults.push({
                            anchorName,
                            syncSuccess: false,
                            syncError: error.message
                        });
                    }
                }

                layer.close(syncLoadingIndex);

                // 显示最终结果
                const successCount = results.filter(r => r.success).length;
                const failCount = results.filter(r => !r.success).length;
                const syncSuccessCount = syncResults.filter(r => r.syncSuccess).length;
                const syncFailCount = syncResults.filter(r => !r.syncSuccess).length;

                let message = `创建完成！成功: ${successCount}个，失败: ${failCount}个`;
                if (successCount > 0) {
                    message += `\n同步完成！成功: ${syncSuccessCount}个，失败: ${syncFailCount}个`;
                }

                if (failCount > 0) {
                    const failedAnchors = results.filter(r => !r.success).map(r => r.anchorName).join(', ');
                    message += `\n创建失败的主播: ${failedAnchors}`;
                }

                if (syncFailCount > 0) {
                    const syncFailedAnchors = syncResults.filter(r => !r.syncSuccess).map(r => r.anchorName).join(', ');
                    message += `\n同步失败的主播: ${syncFailedAnchors}`;
                }

                layer.msg(message, {
                    icon: successCount > 0 ? 1 : 2,
                    time: 5000
                });

                // 如果有成功的，刷新页面数据
                if (successCount > 0) {
                    hideCreateLivePlanModal();
                    // 刷新直播计划列表
                    if (typeof loadLivePlansAndStats === 'function') {
                        loadLivePlansAndStats();
                    }
                }

            } catch (error) {
                layer.close(syncLoadingIndex);
                console.error('同步直播计划失败:', error);

                // 显示创建结果，但提示同步失败
                const successCount = results.filter(r => r.success).length;
                const failCount = results.filter(r => !r.success).length;

                let message = `创建完成！成功: ${successCount}个，失败: ${failCount}个`;
                message += `\n但同步直播计划失败: ${error.message}`;

                layer.msg(message, { icon: 2, time: 5000 });

                if (successCount > 0) {
                    hideCreateLivePlanModal();
                    if (typeof loadLivePlansAndStats === 'function') {
                        loadLivePlansAndStats();
                    }
                }
            }
        } else {
            layer.close(loadingIndex);

            // 显示结果
            const successCount = results.filter(r => r.success).length;
            const failCount = results.filter(r => !r.success).length;

            let message = `创建完成！成功: ${successCount}个，失败: ${failCount}个`;
            if (failCount > 0) {
                const failedAnchors = results.filter(r => !r.success).map(r => r.anchorName).join(', ');
                message += `\n失败的主播: ${failedAnchors}`;
            }

            layer.msg(message, {
                icon: successCount > 0 ? 1 : 2,
                time: 3000
            });
        }

    } catch (error) {
        layer.close(loadingIndex);
        console.error('创建直播计划失败:', error);
        layer.msg('创建直播计划失败: ' + error.message, { icon: 2 });
    }
}

/**
 * 为单个主播创建直播计划
 */
async function createSingleLivePlan(anchorName, appointmentTime, liveEndTime) {
    try {
        const response = await fetch('/api/live-plans/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...addApiKeyHeader()
            },
            body: JSON.stringify({
                anchorName: anchorName,
                appointmentTime: appointmentTime,
                liveEndTime: liveEndTime
            })
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                throw new Error('认证失败');
            }
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || result.error || '创建失败');
        }

        return result;
    } catch (error) {
        console.error(`为主播 ${anchorName} 创建直播计划失败:`, error);
        throw error;
    }
}

/**
 * 同步单个主播的直播计划
 */
async function syncAnchorLivePlan(anchorName) {
    try {
        const response = await fetch('/api/live-plans/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                ...addApiKeyHeader()
            },
            body: JSON.stringify({
                anchorName: anchorName
            })
        });

        if (response.status === 401) {
            const data = await response.json();
            if (data.error === "invalid_api_key") {
                showApiKeyModal();
                throw new Error('认证失败');
            }
        }

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.message || result.error || '同步失败');
        }

        console.log(`主播 ${anchorName} 直播计划同步成功:`, result.data);
        return result;
    } catch (error) {
        console.error(`同步主播 ${anchorName} 直播计划失败:`, error);
        throw error;
    }
}

// 页面加载完成后初始化
$(document).ready(function() {
    console.log('创建直播计划模块已加载');
});
